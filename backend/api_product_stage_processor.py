"""
API Product and Stage Processor Script
Processes user activity data and product information to determine appropriate stages and products.

This script provides API functionality to process user activity and product data
without reading/writing JSON or CSV files. Data is passed as JSON strings via POST API.

Input Format (JSON string):
[
    {
        "email": {
            "userActivity": {
                "PageLoad": [
                    {
                        "Page_url": "",
                        "Meta_title": "",
                        "Meta_description": "",
                        "Location": {
                            "Country": "",
                            "City": ""
                        }
                    }
                ]
            },
            "formData": {
                "Email": "",
                "Data": ""
            }
        }
    }
]

Products Format:
{
    "name": "",
    "url": "",
    "features": "",
    "type": "",
    "Stages": [
        {
            "serialNumber": "",
            "Name": "",
            "nextStage": ""
        }
    ]
}

Output Format:
Processed user data with matched products and determined stages
"""

import json
import re
from typing import Dict, List, Any, Optional
from datetime import datetime
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def process_user_activity_and_products(user_data: str, products_data: str) -> List[Dict[str, Any]]:
    """
    Process user activity data and match with products to determine stages.
    
    Args:
        user_data (str): JSON string containing user activity data
        products_data (str): JSON string containing product information
        
    Returns:
        List[Dict]: Processed user data with matched products and stages
    """
    try:
        # Parse input JSON
        users = json.loads(user_data) if isinstance(user_data, str) else user_data
        products = json.loads(products_data) if isinstance(products_data, str) else products_data
        
        processed_users = []
        
        for user_entry in users:
            if "email" in user_entry:
                user_info = user_entry["email"]
                
                # Process user activity
                processed_user = process_single_user(user_info, products)
                processed_users.append(processed_user)
        
        return processed_users
        
    except Exception as e:
        print(f"Error processing user activity and products: {e}")
        return []

def process_single_user(user_info: Dict[str, Any], products: Dict[str, Any]) -> Dict[str, Any]:
    """
    Process a single user's activity and match with products.
    
    Args:
        user_info: User information including activity and form data
        products: Product information with stages
        
    Returns:
        Dict: Processed user data with matched product and stage
    """
    # Extract user data
    user_activity = user_info.get("userActivity", {})
    form_data = user_info.get("formData", {})
    
    # Initialize processed user data
    processed_user = {
        "user_email": form_data.get("Email", ""),
        "form_data": form_data.get("Data", ""),
        "activity_summary": "",
        "matched_product": {},
        "determined_stage": "",
        "next_stage": "",
        "stage_description": "",
        "location": {},
        "page_interactions": [],
        "behavior_score": 0,
        "engagement_level": "low",
        "processed_at": datetime.now().isoformat()
    }
    
    # Process page load activities
    page_loads = user_activity.get("PageLoad", [])
    if page_loads:
        # Get location from first page load
        if page_loads[0].get("Location"):
            processed_user["location"] = page_loads[0]["Location"]
        
        # Analyze page interactions
        page_interactions = analyze_page_interactions(page_loads, products)
        processed_user["page_interactions"] = page_interactions
        
        # Generate activity summary
        processed_user["activity_summary"] = generate_activity_summary(page_loads, form_data)
        
        # Match product based on activity
        matched_product = match_product_from_activity(page_loads, products)
        processed_user["matched_product"] = matched_product
        
        # Determine stage based on activity and product
        stage_info = determine_user_stage(page_loads, form_data, matched_product)
        processed_user.update(stage_info)
        
        # Calculate behavior score
        processed_user["behavior_score"] = calculate_behavior_score(page_loads, form_data)
        
        # Determine engagement level
        processed_user["engagement_level"] = determine_engagement_level(processed_user["behavior_score"])
    
    return processed_user

def analyze_page_interactions(page_loads: List[Dict], products: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Analyze page interactions to understand user behavior.
    
    Args:
        page_loads: List of page load events
        products: Product information
        
    Returns:
        List[Dict]: Analyzed page interactions
    """
    interactions = []
    
    for page in page_loads:
        interaction = {
            "page_url": page.get("Page_url", ""),
            "meta_title": page.get("Meta_title", ""),
            "meta_description": page.get("Meta_description", ""),
            "page_type": classify_page_type(page),
            "product_relevance": calculate_product_relevance(page, products),
            "intent_score": calculate_intent_score(page)
        }
        interactions.append(interaction)
    
    return interactions

def classify_page_type(page: Dict[str, Any]) -> str:
    """
    Classify the type of page based on URL and metadata.
    
    Args:
        page: Page information
        
    Returns:
        str: Page type classification
    """
    url = page.get("Page_url", "").lower()
    title = page.get("Meta_title", "").lower()
    description = page.get("Meta_description", "").lower()
    
    # Product page indicators
    if any(keyword in url for keyword in ["product", "course", "program", "item"]):
        return "product_page"
    
    # Landing page indicators
    if any(keyword in url for keyword in ["landing", "lp", "campaign"]):
        return "landing_page"
    
    # Home page indicators
    if url.endswith("/") or "home" in url:
        return "home_page"
    
    # Blog/content indicators
    if any(keyword in url for keyword in ["blog", "article", "content"]):
        return "content_page"
    
    # Pricing page indicators
    if any(keyword in url for keyword in ["pricing", "price", "cost"]):
        return "pricing_page"
    
    # Contact/form page indicators
    if any(keyword in url for keyword in ["contact", "form", "signup", "register"]):
        return "contact_page"
    
    return "other"

def calculate_product_relevance(page: Dict[str, Any], products: Dict[str, Any]) -> float:
    """
    Calculate how relevant a page is to the products.
    
    Args:
        page: Page information
        products: Product information
        
    Returns:
        float: Relevance score (0-1)
    """
    product_name = products.get("name", "").lower()
    product_features = " ".join(products.get("features", [])).lower()
    
    page_text = f"{page.get('Meta_title', '')} {page.get('Meta_description', '')} {page.get('Page_url', '')}".lower()
    
    # Simple keyword matching
    relevance_score = 0.0
    
    # Check for product name
    if product_name and product_name in page_text:
        relevance_score += 0.5
    
    # Check for product features
    feature_words = product_features.split()
    matching_features = sum(1 for word in feature_words if word in page_text and len(word) > 3)
    if feature_words:
        relevance_score += (matching_features / len(feature_words)) * 0.3
    
    # Check for product type
    product_type = products.get("type", "").lower()
    if product_type and product_type in page_text:
        relevance_score += 0.2
    
    return min(relevance_score, 1.0)

def calculate_intent_score(page: Dict[str, Any]) -> float:
    """
    Calculate user intent score based on page characteristics.
    
    Args:
        page: Page information
        
    Returns:
        float: Intent score (0-1)
    """
    page_type = classify_page_type(page)
    
    # Intent scores by page type
    intent_scores = {
        "product_page": 0.8,
        "pricing_page": 0.9,
        "contact_page": 0.7,
        "landing_page": 0.6,
        "content_page": 0.3,
        "home_page": 0.2,
        "other": 0.1
    }
    
    return intent_scores.get(page_type, 0.1)

def generate_activity_summary(page_loads: List[Dict], form_data: Dict[str, Any]) -> str:
    """
    Generate a summary of user activity.
    
    Args:
        page_loads: List of page load events
        form_data: Form submission data
        
    Returns:
        str: Activity summary
    """
    if not page_loads:
        return "No page activity recorded"
    
    summary_parts = []
    
    # Page visit summary
    unique_pages = len(set(page.get("Page_url", "") for page in page_loads))
    summary_parts.append(f"Visited {unique_pages} unique page(s)")
    
    # Page types visited
    page_types = [classify_page_type(page) for page in page_loads]
    unique_types = list(set(page_types))
    if unique_types:
        summary_parts.append(f"Viewed {', '.join(unique_types)}")
    
    # Form interaction
    if form_data.get("Email") or form_data.get("Data"):
        summary_parts.append("Submitted form data")
    
    # Location info
    if page_loads[0].get("Location", {}).get("Country"):
        country = page_loads[0]["Location"]["Country"]
        summary_parts.append(f"Located in {country}")
    
    return ". ".join(summary_parts) + "."

def match_product_from_activity(page_loads: List[Dict], products: Dict[str, Any]) -> Dict[str, Any]:
    """
    Match product based on user activity.
    
    Args:
        page_loads: List of page load events
        products: Product information
        
    Returns:
        Dict: Matched product information
    """
    # Calculate overall relevance score
    total_relevance = 0.0
    for page in page_loads:
        total_relevance += calculate_product_relevance(page, products)
    
    avg_relevance = total_relevance / len(page_loads) if page_loads else 0.0
    
    # Return product with relevance score
    matched_product = products.copy()
    matched_product["relevance_score"] = avg_relevance
    matched_product["match_confidence"] = "high" if avg_relevance > 0.5 else "medium" if avg_relevance > 0.2 else "low"
    
    return matched_product

def determine_user_stage(page_loads: List[Dict], form_data: Dict[str, Any], matched_product: Dict[str, Any]) -> Dict[str, Any]:
    """
    Determine user stage based on activity and product.
    
    Args:
        page_loads: List of page load events
        form_data: Form submission data
        matched_product: Matched product information
        
    Returns:
        Dict: Stage information
    """
    stages = matched_product.get("Stages", [])
    if not stages:
        return {
            "determined_stage": "Unknown",
            "next_stage": "",
            "stage_description": "No stages defined for product"
        }
    
    # Determine stage based on activity
    stage_scores = {}
    
    for stage in stages:
        stage_name = stage.get("Name", "")
        stage_scores[stage_name] = calculate_stage_score(page_loads, form_data, stage)
    
    # Find stage with highest score
    if stage_scores:
        determined_stage_name = max(stage_scores, key=stage_scores.get)
        determined_stage = next((s for s in stages if s.get("Name") == determined_stage_name), stages[0])
        
        return {
            "determined_stage": determined_stage.get("Name", ""),
            "next_stage": determined_stage.get("nextStage", ""),
            "stage_description": f"User is in {determined_stage_name} stage",
            "stage_confidence": stage_scores[determined_stage_name]
        }
    
    # Default to first stage
    first_stage = stages[0]
    return {
        "determined_stage": first_stage.get("Name", ""),
        "next_stage": first_stage.get("nextStage", ""),
        "stage_description": f"User is in {first_stage.get('Name', '')} stage",
        "stage_confidence": 0.5
    }

def calculate_stage_score(page_loads: List[Dict], form_data: Dict[str, Any], stage: Dict[str, Any]) -> float:
    """
    Calculate how well user activity matches a stage.
    
    Args:
        page_loads: List of page load events
        form_data: Form submission data
        stage: Stage information
        
    Returns:
        float: Stage match score (0-1)
    """
    stage_name = stage.get("Name", "").lower()
    score = 0.0
    
    # Stage-specific scoring logic
    if "awareness" in stage_name or "visitor" in stage_name:
        # Early stage - basic page views
        score += 0.3 if page_loads else 0.0
        score += 0.2 if any(classify_page_type(p) == "home_page" for p in page_loads) else 0.0
    
    elif "consideration" in stage_name or "interest" in stage_name:
        # Middle stage - product page views
        score += 0.4 if any(classify_page_type(p) == "product_page" for p in page_loads) else 0.0
        score += 0.3 if any(classify_page_type(p) == "pricing_page" for p in page_loads) else 0.0
    
    elif "purchase" in stage_name or "conversion" in stage_name:
        # Late stage - form submissions, contact pages
        score += 0.5 if form_data.get("Email") or form_data.get("Data") else 0.0
        score += 0.3 if any(classify_page_type(p) == "contact_page" for p in page_loads) else 0.0
    
    # General activity indicators
    if len(page_loads) > 1:
        score += 0.1  # Multiple page views
    
    return min(score, 1.0)

def calculate_behavior_score(page_loads: List[Dict], form_data: Dict[str, Any]) -> float:
    """
    Calculate overall user behavior score.
    
    Args:
        page_loads: List of page load events
        form_data: Form submission data
        
    Returns:
        float: Behavior score (0-100)
    """
    score = 0.0
    
    # Page view scoring
    score += len(page_loads) * 10  # 10 points per page view
    
    # Page type scoring
    for page in page_loads:
        page_type = classify_page_type(page)
        type_scores = {
            "product_page": 20,
            "pricing_page": 25,
            "contact_page": 15,
            "landing_page": 10,
            "content_page": 5,
            "home_page": 5,
            "other": 2
        }
        score += type_scores.get(page_type, 0)
    
    # Form submission scoring
    if form_data.get("Email"):
        score += 30
    if form_data.get("Data"):
        score += 20
    
    return min(score, 100.0)

def determine_engagement_level(behavior_score: float) -> str:
    """
    Determine engagement level based on behavior score.
    
    Args:
        behavior_score: Calculated behavior score
        
    Returns:
        str: Engagement level
    """
    if behavior_score >= 70:
        return "high"
    elif behavior_score >= 40:
        return "medium"
    else:
        return "low"

# Example usage function for testing
def test_user_processing():
    """Test function to demonstrate usage"""
    test_users = [
        {
            "email": {
                "userActivity": {
                    "PageLoad": [
                        {
                            "Page_url": "https://example.com/product/ai-course",
                            "Meta_title": "AI Course - Learn Machine Learning",
                            "Meta_description": "Comprehensive AI course with hands-on projects",
                            "Location": {
                                "Country": "India",
                                "City": "Mumbai"
                            }
                        },
                        {
                            "Page_url": "https://example.com/pricing",
                            "Meta_title": "Pricing - AI Course",
                            "Meta_description": "Affordable pricing for AI learning",
                            "Location": {
                                "Country": "India",
                                "City": "Mumbai"
                            }
                        }
                    ]
                },
                "formData": {
                    "Email": "<EMAIL>",
                    "Data": "Interested in AI course"
                }
            }
        }
    ]
    
    test_products = {
        "name": "AI Master Course",
        "url": "https://example.com/ai-course",
        "features": ["Machine Learning", "Deep Learning", "Projects"],
        "type": "Online Course",
        "Stages": [
            {
                "serialNumber": "1",
                "Name": "Awareness",
                "nextStage": "Consideration"
            },
            {
                "serialNumber": "2", 
                "Name": "Consideration",
                "nextStage": "Purchase"
            }
        ]
    }
    
    result = process_user_activity_and_products(json.dumps(test_users), json.dumps(test_products))
    return result

if __name__ == "__main__":
    # Test the function
    processed_users = test_user_processing()
    print(f"Processed {len(processed_users)} users")
    for user in processed_users:
        print(f"Email: {user.get('user_email')}")
        print(f"Stage: {user.get('determined_stage')}")
        print(f"Engagement: {user.get('engagement_level')}")
        print("---")
