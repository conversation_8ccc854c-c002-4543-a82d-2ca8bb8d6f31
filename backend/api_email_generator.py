"""
API Email Generator Script
Generates personalized emails based on user activity, product, stage, and template data.

This script provides API functionality to generate emails without reading/writing JSON or CSV files.
Data is passed as JSON strings via POST API.

Input Format (JSON string):
{
    "users": [
        {
            "email": "<EMAIL>",
            "userActivity": {...},
            "formData": {...},
            "product": {...},
            "stage": {...},
            "templates": [...]
        }
    ]
}

Output Format:
List of dictionaries with subject, preheader, mail content, and timing for each user
"""

import json
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from langchain_openai import ChatOpenAI
from langchain.prompts import Chat<PERSON>romptTemplate
import os
import re
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def generate_personalized_emails(input_data: str) -> List[Dict[str, Any]]:
    """
    Generate personalized emails for users based on their activity and context.
    
    Args:
        input_data (str): JSON string containing user data with activity, product, stage, and templates
        
    Returns:
        List[Dict]: List of generated emails with subject, preheader, content, and timing
    """
    try:
        # Parse input JSON
        data = json.loads(input_data) if isinstance(input_data, str) else input_data
        users = data.get('users', [])
        
        # Initialize LLM
        llm = ChatOpenAI(
            model="gpt-4o-mini",
            temperature=0.7,
            max_tokens=1500
        )
        
        # Generate emails for all users
        generated_emails = []
        
        for user in users:
            try:
                email_result = generate_user_email(llm, user)
                generated_emails.append(email_result)
            except Exception as e:
                print(f"Error generating email for user {user.get('email', 'unknown')}: {e}")
                # Add error result
                error_result = {
                    "user_email": user.get('email', ''),
                    "subject": "Error generating email",
                    "preheader": "",
                    "mail_content": "Error occurred during email generation",
                    "send_time": datetime.now().isoformat(),
                    "error": str(e),
                    "generated_at": datetime.now().isoformat()
                }
                generated_emails.append(error_result)
        
        return generated_emails
        
    except Exception as e:
        print(f"Error in generate_personalized_emails: {e}")
        return []

def generate_user_email(llm: ChatOpenAI, user_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate personalized email for a single user.
    
    Args:
        llm: Language model instance
        user_data: User data including activity, product, stage, and templates
        
    Returns:
        Dict: Generated email with subject, preheader, content, and timing
    """
    # Extract user information
    user_email = user_data.get('email', '')
    user_activity = user_data.get('userActivity', {})
    form_data = user_data.get('formData', {})
    product = user_data.get('product', {})
    stage = user_data.get('stage', {})
    templates = user_data.get('templates', [])
    
    # Analyze user behavior
    behavior_analysis = analyze_user_behavior(user_activity, form_data)
    
    # Select best template
    selected_template = select_best_template(templates, stage, behavior_analysis)
    
    # Generate personalized content
    email_content = generate_email_content(
        llm=llm,
        user_email=user_email,
        behavior_analysis=behavior_analysis,
        product=product,
        stage=stage,
        template=selected_template
    )
    
    # Calculate optimal send time
    send_time = calculate_optimal_send_time(behavior_analysis, user_activity)
    
    # Compile final result
    result = {
        "user_email": user_email,
        "subject": email_content.get('subject', ''),
        "preheader": email_content.get('preheader', ''),
        "mail_content": email_content.get('body', ''),
        "send_time": send_time,
        "template_used": selected_template.get('template_name', '') if selected_template else '',
        "stage": stage.get('Name', ''),
        "product_name": product.get('name', ''),
        "behavior_score": behavior_analysis.get('score', 0),
        "personalization_level": behavior_analysis.get('personalization_level', 'low'),
        "generated_at": datetime.now().isoformat()
    }
    
    return result

def analyze_user_behavior(user_activity: Dict[str, Any], form_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Analyze user behavior to understand engagement and preferences.
    
    Args:
        user_activity: User activity data
        form_data: Form submission data
        
    Returns:
        Dict: Behavior analysis results
    """
    analysis = {
        "score": 0,
        "engagement_level": "low",
        "interests": [],
        "page_types_visited": [],
        "form_submitted": False,
        "location": {},
        "personalization_level": "low",
        "behavior_summary": ""
    }
    
    # Analyze page loads
    page_loads = user_activity.get("PageLoad", [])
    if page_loads:
        # Extract location
        if page_loads[0].get("Location"):
            analysis["location"] = page_loads[0]["Location"]
        
        # Analyze page types
        page_types = []
        interests = []
        
        for page in page_loads:
            page_url = page.get("Page_url", "")
            meta_title = page.get("Meta_title", "")
            meta_description = page.get("Meta_description", "")
            
            # Classify page type
            page_type = classify_page_type(page_url, meta_title)
            page_types.append(page_type)
            
            # Extract interests
            page_interests = extract_interests(meta_title, meta_description)
            interests.extend(page_interests)
        
        analysis["page_types_visited"] = list(set(page_types))
        analysis["interests"] = list(set(interests))
        
        # Calculate engagement score
        analysis["score"] = calculate_engagement_score(page_loads, form_data)
        analysis["engagement_level"] = determine_engagement_level(analysis["score"])
        
        # Determine personalization level
        analysis["personalization_level"] = determine_personalization_level(analysis)
        
        # Generate behavior summary
        analysis["behavior_summary"] = generate_behavior_summary(analysis, page_loads, form_data)
    
    # Check form submission
    if form_data.get("Email") or form_data.get("Data"):
        analysis["form_submitted"] = True
        analysis["score"] += 20  # Bonus for form submission
    
    return analysis

def classify_page_type(page_url: str, meta_title: str) -> str:
    """Classify page type based on URL and title."""
    url_lower = page_url.lower()
    title_lower = meta_title.lower()
    
    if any(keyword in url_lower for keyword in ["product", "course", "program"]):
        return "product"
    elif any(keyword in url_lower for keyword in ["pricing", "price", "cost"]):
        return "pricing"
    elif any(keyword in url_lower for keyword in ["contact", "form", "signup"]):
        return "contact"
    elif any(keyword in url_lower for keyword in ["blog", "article", "content"]):
        return "content"
    elif url_lower.endswith("/") or "home" in url_lower:
        return "home"
    else:
        return "other"

def extract_interests(meta_title: str, meta_description: str) -> List[str]:
    """Extract user interests from page metadata."""
    text = f"{meta_title} {meta_description}".lower()
    
    # Common interest keywords
    interest_keywords = [
        "ai", "artificial intelligence", "machine learning", "data science",
        "programming", "coding", "development", "technology", "course",
        "learning", "education", "training", "certification", "skill"
    ]
    
    found_interests = []
    for keyword in interest_keywords:
        if keyword in text:
            found_interests.append(keyword)
    
    return found_interests

def calculate_engagement_score(page_loads: List[Dict], form_data: Dict[str, Any]) -> int:
    """Calculate user engagement score based on activity."""
    score = 0
    
    # Page view scoring
    score += len(page_loads) * 10
    
    # Page type scoring
    for page in page_loads:
        page_type = classify_page_type(page.get("Page_url", ""), page.get("Meta_title", ""))
        type_scores = {
            "product": 25,
            "pricing": 30,
            "contact": 20,
            "content": 10,
            "home": 5,
            "other": 2
        }
        score += type_scores.get(page_type, 0)
    
    # Form submission bonus
    if form_data.get("Email") or form_data.get("Data"):
        score += 30
    
    return min(score, 100)

def determine_engagement_level(score: int) -> str:
    """Determine engagement level from score."""
    if score >= 70:
        return "high"
    elif score >= 40:
        return "medium"
    else:
        return "low"

def determine_personalization_level(analysis: Dict[str, Any]) -> str:
    """Determine how personalized the email should be."""
    score = analysis.get("score", 0)
    interests = analysis.get("interests", [])
    form_submitted = analysis.get("form_submitted", False)
    
    if form_submitted and len(interests) > 2 and score > 60:
        return "high"
    elif len(interests) > 0 and score > 30:
        return "medium"
    else:
        return "low"

def generate_behavior_summary(analysis: Dict[str, Any], page_loads: List[Dict], form_data: Dict[str, Any]) -> str:
    """Generate a summary of user behavior."""
    summary_parts = []
    
    # Page activity
    if page_loads:
        summary_parts.append(f"Visited {len(page_loads)} page(s)")
        
        page_types = analysis.get("page_types_visited", [])
        if page_types:
            summary_parts.append(f"Viewed {', '.join(page_types)} pages")
    
    # Interests
    interests = analysis.get("interests", [])
    if interests:
        summary_parts.append(f"Showed interest in {', '.join(interests[:3])}")
    
    # Form submission
    if analysis.get("form_submitted"):
        summary_parts.append("Submitted contact form")
    
    # Location
    location = analysis.get("location", {})
    if location.get("Country"):
        summary_parts.append(f"Located in {location['Country']}")
    
    return ". ".join(summary_parts) + "." if summary_parts else "Limited activity recorded"

def select_best_template(templates: List[Dict], stage: Dict[str, Any], behavior_analysis: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Select the best template based on stage and behavior.
    
    Args:
        templates: Available templates
        stage: User stage information
        behavior_analysis: User behavior analysis
        
    Returns:
        Dict: Best matching template or None
    """
    if not templates:
        return None
    
    stage_name = stage.get("Name", "").lower()
    engagement_level = behavior_analysis.get("engagement_level", "low")
    
    # Score templates based on relevance
    template_scores = []
    
    for template in templates:
        score = 0
        template_stage = template.get("stage", "").lower()
        
        # Stage matching
        if template_stage == stage_name:
            score += 50
        elif any(keyword in template_stage for keyword in stage_name.split()):
            score += 25
        
        # Engagement level matching
        template_content = f"{template.get('subject', '')} {template.get('body', '')}".lower()
        
        if engagement_level == "high" and any(word in template_content for word in ["exclusive", "premium", "advanced"]):
            score += 20
        elif engagement_level == "medium" and any(word in template_content for word in ["discover", "learn", "explore"]):
            score += 15
        elif engagement_level == "low" and any(word in template_content for word in ["introduction", "welcome", "start"]):
            score += 10
        
        template_scores.append((template, score))
    
    # Return template with highest score
    if template_scores:
        best_template = max(template_scores, key=lambda x: x[1])[0]
        return best_template
    
    return templates[0]  # Fallback to first template

def generate_email_content(
    llm: ChatOpenAI,
    user_email: str,
    behavior_analysis: Dict[str, Any],
    product: Dict[str, Any],
    stage: Dict[str, Any],
    template: Optional[Dict[str, Any]]
) -> Dict[str, Any]:
    """
    Generate personalized email content using LLM.
    
    Args:
        llm: Language model instance
        user_email: User email address
        behavior_analysis: User behavior analysis
        product: Product information
        stage: Stage information
        template: Selected template
        
    Returns:
        Dict: Generated email content
    """
    # Create personalization prompt
    prompt_template = ChatPromptTemplate.from_template("""
    You are an expert email marketing copywriter. Generate a highly personalized email based on the following information:

    User Email: {user_email}
    User Behavior Summary: {behavior_summary}
    Engagement Level: {engagement_level}
    Personalization Level: {personalization_level}
    User Interests: {interests}
    User Location: {location}

    Product Information:
    - Name: {product_name}
    - Features: {product_features}
    - Type: {product_type}
    - URL: {product_url}

    Stage Information:
    - Current Stage: {current_stage}
    - Next Stage: {next_stage}
    - Stage Description: {stage_description}

    Template Reference (if available):
    - Subject: {template_subject}
    - Body: {template_body}

    Instructions:
    1. Create a compelling subject line that matches the user's engagement level
    2. Write a preheader that complements the subject (50-90 characters)
    3. Generate personalized email body content that:
       - References the user's specific behavior and interests
       - Matches their engagement level and stage
       - Includes relevant product features
       - Creates appropriate urgency for the stage
       - Avoids using single or double quotes in the generated text
       - Uses a conversational, engaging tone

    Personalization Guidelines:
    - High engagement: Use exclusive language, advanced features, time-sensitive offers
    - Medium engagement: Focus on benefits, social proof, educational content
    - Low engagement: Use welcoming tone, basic introduction, low-pressure approach

    Return the response in this exact JSON format:
    {{
        "subject": "Your personalized subject line here",
        "preheader": "Your preheader text here",
        "body": "Your personalized email body content here"
    }}
    """)
    
    # Prepare template data
    template_subject = template.get('subject', '') if template else ''
    template_body = template.get('body', '') if template else ''
    
    # Prepare prompt variables
    prompt_vars = {
        "user_email": user_email,
        "behavior_summary": behavior_analysis.get('behavior_summary', ''),
        "engagement_level": behavior_analysis.get('engagement_level', 'low'),
        "personalization_level": behavior_analysis.get('personalization_level', 'low'),
        "interests": ', '.join(behavior_analysis.get('interests', [])),
        "location": f"{behavior_analysis.get('location', {}).get('City', '')}, {behavior_analysis.get('location', {}).get('Country', '')}".strip(', '),
        "product_name": product.get('name', ''),
        "product_features": ', '.join(product.get('features', [])) if isinstance(product.get('features'), list) else str(product.get('features', '')),
        "product_type": product.get('type', ''),
        "product_url": product.get('url', ''),
        "current_stage": stage.get('Name', ''),
        "next_stage": stage.get('nextStage', ''),
        "stage_description": f"User is in {stage.get('Name', '')} stage",
        "template_subject": template_subject,
        "template_body": template_body
    }
    
    try:
        # Generate content
        chain = prompt_template | llm
        response = chain.invoke(prompt_vars)
        
        # Parse response
        try:
            content = json.loads(response.content)
            return content
        except json.JSONDecodeError:
            # Fallback parsing
            return parse_email_content_fallback(response.content)
            
    except Exception as e:
        print(f"Error generating email content: {e}")
        # Return fallback content
        return {
            "subject": f"Personalized update for {user_email.split('@')[0]}",
            "preheader": "We have something special for you",
            "body": f"Hello! Based on your recent activity, we thought you might be interested in {product.get('name', 'our product')}. Let us know if you have any questions!"
        }

def parse_email_content_fallback(content: str) -> Dict[str, Any]:
    """Parse email content when JSON parsing fails."""
    import re
    
    result = {"subject": "", "preheader": "", "body": ""}
    
    # Try to extract fields using regex
    for field in ["subject", "preheader", "body"]:
        pattern = rf'"{field}":\s*"([^"]*)"'
        match = re.search(pattern, content, re.IGNORECASE | re.DOTALL)
        if match:
            result[field] = match.group(1)
    
    return result

def calculate_optimal_send_time(behavior_analysis: Dict[str, Any], user_activity: Dict[str, Any]) -> str:
    """
    Calculate optimal send time based on user behavior.
    
    Args:
        behavior_analysis: User behavior analysis
        user_activity: User activity data
        
    Returns:
        str: ISO format timestamp for optimal send time
    """
    # Base send time (next business day at 10 AM)
    base_time = datetime.now().replace(hour=10, minute=0, second=0, microsecond=0)
    
    # Adjust based on engagement level
    engagement_level = behavior_analysis.get("engagement_level", "low")
    
    if engagement_level == "high":
        # Send within 2 hours for high engagement
        send_time = datetime.now() + timedelta(hours=2)
    elif engagement_level == "medium":
        # Send within 24 hours for medium engagement
        send_time = datetime.now() + timedelta(hours=24)
    else:
        # Send within 48 hours for low engagement
        send_time = base_time + timedelta(days=1)
    
    # Ensure business hours (9 AM - 6 PM)
    if send_time.hour < 9:
        send_time = send_time.replace(hour=9)
    elif send_time.hour > 18:
        send_time = send_time.replace(hour=10) + timedelta(days=1)
    
    return send_time.isoformat()

# Example usage function for testing
def test_email_generation():
    """Test function to demonstrate usage"""
    test_input = {
        "users": [
            {
                "email": "<EMAIL>",
                "userActivity": {
                    "PageLoad": [
                        {
                            "Page_url": "https://example.com/ai-course",
                            "Meta_title": "AI Course - Machine Learning",
                            "Meta_description": "Learn AI and ML with hands-on projects",
                            "Location": {"Country": "India", "City": "Mumbai"}
                        }
                    ]
                },
                "formData": {
                    "Email": "<EMAIL>",
                    "Data": "Interested in AI course"
                },
                "product": {
                    "name": "AI Master Course",
                    "features": ["Machine Learning", "Deep Learning"],
                    "type": "Online Course",
                    "url": "https://example.com/ai-course"
                },
                "stage": {
                    "Name": "Consideration",
                    "nextStage": "Purchase"
                },
                "templates": [
                    {
                        "template_name": "consideration_template",
                        "subject": "Ready to advance your AI skills?",
                        "body": "Discover our comprehensive AI course...",
                        "stage": "consideration"
                    }
                ]
            }
        ]
    }
    
    result = generate_personalized_emails(json.dumps(test_input))
    return result

if __name__ == "__main__":
    # Test the function
    emails = test_email_generation()
    print(f"Generated {len(emails)} emails")
    for email in emails:
        print(f"User: {email.get('user_email')}")
        print(f"Subject: {email.get('subject')}")
        print(f"Send Time: {email.get('send_time')}")
        print("---")
