BACKE<PERSON> SCRIPTS DEPENDENCY MAPPING
=====================================

This document outlines the dependencies and data flow between all backend scripts in the /backend/ folder.

1. DATA GENERATION FLOW
=======================

1.1 PRIMARY DATA GENERATION
---------------------------
synthetic_user_data_generation.py
├── CREATES: {organization_name}_processed_user_data.csv
├── LOCATION: Sample Data For Mass Generation/
├── CONTAINS: user_email, first_name, country, content_medium, job_role, content_viewed, user_behaviour, user_stage, engagement_status, last_product_sent, Last_Open_Time, Last_Click_Time, Send_Time
└── DEPENDENCIES:
    ├── READS: data/product_details.json
    ├── READS: data/user_journey.json
    ├── READS: data/users.json
    └── USES: GPT-4o-mini for behavior generation

1.2 FUNNEL DATA PROCESSING
---------------------------
synthetic_user_data_funnel.py
├── READS: {organization_name}_processed_user_data.csv (from synthetic_user_data_generation.py)
├── CREATES: funnel_visualization.html
├── PROCESSES: Stage-based user filtering and funnel progression
└── DEPENDENCIES:
    ├── READS: data/user_journey.json (for stage ordering by s_no)
    ├── READS: data/users.json (for organization detection)
    └── REQUIRES: Output from synthetic_user_data_generation.py

2. EMAIL GENERATION FLOW
=========================

2.1 TEMPLATE-BASED EMAIL GENERATION
------------------------------------
mass_email_generator.py
├── READS: {organization_name}_processed_user_data.csv (from synthetic_user_data_generation.py)
├── CREATES: mass_email_results_{timestamp}.csv
├── CREATES: data/html_emails/mass_email_{timestamp}_{idx}.html
├── CREATES: Sample Data For Mass Generation/user_popup_data.csv
└── DEPENDENCIES:
    ├── READS: data/templates/{stage}.json (template files)
    ├── READS: data/product_details.json
    ├── READS: data/communication_settings.json
    ├── USES: core/product_selector.py (product matching)
    ├── USES: core/email_formatter.py (HTML conversion)
    └── REQUIRES: Output from synthetic_user_data_generation.py

2.2 PERSONALIZED EMAIL GENERATION
----------------------------------
synthetic_user_data_mail_generation.py
├── READS: {organization_name}_processed_user_data.csv (from synthetic_user_data_generation.py)
├── CREATES: mass_email_results_{timestamp}.csv
├── CREATES: data/html_emails/mass_email_{timestamp}_{idx}.html
└── DEPENDENCIES:
    ├── READS: data/product_details.json
    ├── READS: data/communication_settings.json
    ├── USES: GPT-4o-mini for email content generation
    └── REQUIRES: Output from synthetic_user_data_generation.py

3. JOURNEY CREATION FLOW
=========================

3.1 JOURNEY TREE GENERATION
----------------------------
synthetic_user_data_journey_creation.py
├── CREATES: data/node_emails/{node_id}.json
├── CREATES: journey_tree.html
├── CREATES: journey_flow.html
├── CREATES: data/logs/click_logs.json
└── DEPENDENCIES:
    ├── READS: data/product_details.json
    ├── READS: data/templates/{stage}.json
    ├── USES: GPT-4o-mini for node email generation
    └── CAN USE: User data from synthetic_user_data_generation.py

3.2 JOURNEY BUILDER BACKEND
----------------------------
journey_maker.py
├── READS: User data from various sources
├── CREATES: Interactive journey trees
├── CREATES: Node-based email sequences
└── DEPENDENCIES:
    ├── READS: data/user_journey.json
    ├── READS: data/product_details.json
    └── INTEGRATES: With journey creation scripts

4. ANALYTICS AND PERFORMANCE
=============================

4.1 FUNNEL ANALYTICS
---------------------
analytics_dashboard_journey_funnel.py
├── READS: Funnel data from synthetic_user_data_funnel.py
├── READS: Email results from mass_email_generator.py
├── CREATES: Analytics visualizations and reports
└── DEPENDENCIES:
    ├── PROCESSES: CSV outputs from multiple scripts
    └── AGGREGATES: Performance metrics

4.2 CAMPAIGN PERFORMANCE
-------------------------
campaign_performance.py
├── READS: Email campaign results
├── ANALYZES: Performance metrics
└── DEPENDENCIES:
    └── PROCESSES: Outputs from email generation scripts

4.3 PERFORMANCE PREDICTOR
--------------------------
performance_predictor.py
├── READS: Historical campaign data
├── PREDICTS: Future performance
└── DEPENDENCIES:
    └── ANALYZES: Data from email generation and analytics scripts

5. COMMUNICATION CHANNELS
==========================

5.1 EMAIL SENDING
------------------
email_sender.py
├── READS: Email content from generation scripts
├── SENDS: Emails via ESP providers
└── DEPENDENCIES:
    ├── USES: HTML emails from mass_email_generator.py
    └── READS: data/communication_settings.json

5.2 WHATSAPP INTEGRATION
-------------------------
whatsapp_sender.py
├── READS: User data for WhatsApp campaigns
├── SENDS: WhatsApp messages
└── DEPENDENCIES:
    ├── USES: User data from synthetic_user_data_generation.py
    └── READS: WhatsApp templates

whatsapp_template_fetcher.py
├── FETCHES: WhatsApp templates
├── MANAGES: Template configurations
└── DEPENDENCIES:
    └── INTEGRATES: With WhatsApp sender

6. UTILITY AND SUPPORT
=======================

6.1 HTML PROCESSING
--------------------
html_email_generator.py
├── CONVERTS: Text emails to HTML
├── APPLIES: Brand styling
└── DEPENDENCIES:
    ├── USED BY: All email generation scripts
    └── READS: Brand guidelines

mail_to_html_converter.py
├── CONVERTS: Plain text to HTML
├── FORMATS: Email content
└── DEPENDENCIES:
    └── USED BY: Email generation scripts

6.2 PRODUCT SELECTION
----------------------
product_selector.py
├── MATCHES: Users with products
├── CALCULATES: Similarity scores
└── DEPENDENCIES:
    ├── USED BY: All email generation scripts
    └── READS: data/product_details.json

6.3 CHANNEL OPTIMIZATION
-------------------------
channel_optimizer.py
├── OPTIMIZES: Communication channels
├── SELECTS: Best channels for users
└── DEPENDENCIES:
    ├── USES: User data from generation scripts
    └── ANALYZES: Engagement patterns

6.4 POPUP MANAGEMENT
---------------------
popup_configuration_manager.py
├── MANAGES: Popup configurations
├── CONFIGURES: Popup behavior
└── DEPENDENCIES:
    └── INTEGRATES: With email generation scripts

popup_text_generator.py
├── GENERATES: Popup content
├── PERSONALIZES: Popup messages
└── DEPENDENCIES:
    ├── USES: User data from generation scripts
    └── READS: data/communication_settings.json

7. KEY DATA FILES AND DEPENDENCIES
===================================

7.1 CONFIGURATION FILES (READ BY MULTIPLE SCRIPTS)
---------------------------------------------------
data/product_details.json
├── USED BY: synthetic_user_data_generation.py
├── USED BY: mass_email_generator.py
├── USED BY: synthetic_user_data_mail_generation.py
├── USED BY: synthetic_user_data_journey_creation.py
└── USED BY: product_selector.py

data/user_journey.json
├── USED BY: synthetic_user_data_generation.py (stage mapping)
├── USED BY: synthetic_user_data_funnel.py (stage ordering)
└── USED BY: journey_maker.py

data/users.json
├── USED BY: synthetic_user_data_generation.py (organization detection)
├── USED BY: synthetic_user_data_funnel.py (organization detection)
└── USED BY: All scripts for organization-specific filtering

data/communication_settings.json
├── USED BY: mass_email_generator.py
├── USED BY: synthetic_user_data_mail_generation.py
└── USED BY: email_sender.py

7.2 TEMPLATE FILES (READ BY EMAIL GENERATION)
----------------------------------------------
data/templates/{stage}.json
├── USED BY: mass_email_generator.py
├── USED BY: synthetic_user_data_journey_creation.py
└── CONTAINS: Stage-specific email templates

7.3 GENERATED CSV FILES (CREATED AND CONSUMED)
-----------------------------------------------
Sample Data For Mass Generation/{organization_name}_processed_user_data.csv
├── CREATED BY: synthetic_user_data_generation.py
├── READ BY: synthetic_user_data_funnel.py
├── READ BY: mass_email_generator.py
└── READ BY: synthetic_user_data_mail_generation.py

Sample Data For Mass Generation/mass_email_results_{timestamp}.csv
├── CREATED BY: mass_email_generator.py
├── CREATED BY: synthetic_user_data_mail_generation.py
└── READ BY: analytics_dashboard_journey_funnel.py

Sample Data For Mass Generation/user_popup_data.csv
├── CREATED BY: mass_email_generator.py
└── USED BY: popup_configuration_manager.py

7.4 HTML OUTPUT FILES
----------------------
data/html_emails/mass_email_{timestamp}_{idx}.html
├── CREATED BY: mass_email_generator.py
├── CREATED BY: synthetic_user_data_mail_generation.py
└── USED BY: email_sender.py

data/node_emails/{node_id}.json
├── CREATED BY: synthetic_user_data_journey_creation.py
└── USED BY: journey_maker.py

8. EXECUTION FLOW RECOMMENDATIONS
==================================

8.1 COMPLETE DATA GENERATION PIPELINE
--------------------------------------
1. Run synthetic_user_data_generation.py
   └── Creates base user data CSV

2. Run synthetic_user_data_funnel.py
   └── Processes funnel visualization

3. Run mass_email_generator.py OR synthetic_user_data_mail_generation.py
   └── Generates email campaigns

4. Run synthetic_user_data_journey_creation.py
   └── Creates journey trees

5. Run analytics_dashboard_journey_funnel.py
   └── Analyzes performance

8.2 DEPENDENCIES SUMMARY
-------------------------
- All scripts depend on configuration files in data/ folder
- Email generation scripts depend on user data from synthetic_user_data_generation.py
- Analytics scripts depend on outputs from generation scripts
- Journey scripts can work independently but benefit from user data
- Utility scripts are used by multiple generation scripts

9. INTEGRATION POINTS
=====================

9.1 SHARED DATA FORMATS
------------------------
- All CSV files use consistent column naming
- User data format standardized across scripts
- Product data structure consistent
- Organization detection unified

9.2 COMMON DEPENDENCIES
------------------------
- GPT-4o-mini model used consistently
- Organization-specific filtering applied uniformly
- Stage-based processing standardized
- HTML formatting consistent across scripts
