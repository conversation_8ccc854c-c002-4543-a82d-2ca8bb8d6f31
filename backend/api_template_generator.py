"""
API Template Generator Script
Generates templates for all stages based on product and organization data.

This script provides API functionality to return templates for all stages
without reading/writing JSON or CSV files. Data is passed as JSON strings via POST API.

Input Format (JSON string):
{
    "product_stages": [],
    "stage_details": {
        "Awareness": {
            "description": "",
            "name": "",
            "nextStage": ""
        }
    },
    "num_templates_per_stage": "",
    "include_emojis": {
        "use_emojis_subject": bool,
        "use_emojis_body": bool
    },
    "product_data": {
        "name": "",
        "features": [],
        "summary": "",
        "url": ""
    },
    "org_url": "",
    "communication_details": {
        "brandArchetype": "",
        "communication_style": "",
        "email_length": "",
        "sender_name": "",
        "toneOfVoice": "",
        "utm_campaign": "",
        "utm_content": "",
        "utm_medium": "",
        "utm_source": ""
    }
}

Output Format:
List of template dictionaries with subject, body, preheader for each stage
"""

import json
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
import os
import yaml
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def load_prompts():
    """Load prompts from config/prompts.yml"""
    try:
        with open('config/prompts.yml', 'r') as f:
            return yaml.safe_load(f)
    except Exception as e:
        print(f"Error loading prompts: {e}")
        return {}

async def generate_templates_for_all_stages(input_data: str) -> List[Dict[str, Any]]:
    """
    Generate templates for all stages based on input data.
    
    Args:
        input_data (str): JSON string containing all required data
        
    Returns:
        List[Dict]: List of generated templates for all stages
    """
    try:
        # Parse input JSON
        data = json.loads(input_data) if isinstance(input_data, str) else input_data
        
        product_stages = data.get('product_stages', [])
        stage_details = data.get('stage_details', {})
        num_templates_per_stage = int(data.get('num_templates_per_stage', 1))
        include_emojis = data.get('include_emojis', {})
        product_data = data.get('product_data', {})
        org_url = data.get('org_url', '')
        communication_details = data.get('communication_details', {})
        
        # Initialize LLM with the same model as repository
        llm_model = "ft:gpt-4o-mini-2024-07-18:analytics-vidhya:oev1mailgen:B7xdiKXj"
        api_key = os.getenv("OPENAI_API_KEY")

        if not api_key:
            print("Warning: OpenAI API key not found.")
            return []

        llm = ChatOpenAI(
            model=llm_model,
            temperature=0.7,
            max_tokens=1000,
            api_key=api_key
        )
        
        # Generate templates for each stage
        all_templates = []
        
        for stage in product_stages:
            stage_info = stage_details.get(stage, {})
            
            # Generate templates for this stage
            stage_templates = await generate_stage_templates(
                llm=llm,
                stage=stage,
                stage_info=stage_info,
                product_data=product_data,
                communication_details=communication_details,
                include_emojis=include_emojis,
                num_templates=num_templates_per_stage,
                org_url=org_url
            )
            
            all_templates.extend(stage_templates)
        
        return all_templates
        
    except Exception as e:
        print(f"Error generating templates: {e}")
        return []

async def generate_stage_templates(
    llm: ChatOpenAI,
    stage: str,
    stage_info: Dict[str, Any],
    product_data: Dict[str, Any],
    communication_details: Dict[str, Any],
    include_emojis: Dict[str, bool],
    num_templates: int,
    org_url: str
) -> List[Dict[str, Any]]:
    """
    Generate templates for a specific stage.
    
    Args:
        llm: Language model instance
        stage: Stage name
        stage_info: Stage details
        product_data: Product information
        communication_details: Communication settings
        include_emojis: Emoji preferences
        num_templates: Number of templates to generate
        org_url: Organization URL
        
    Returns:
        List[Dict]: Generated templates for the stage
    """
    templates = []
    
    # Load prompts from config
    prompts = load_prompts()

    # Use the exact template generation prompt from the repository
    base_prompt = prompts.get('personalized_email_generation', {}).get('template', '')

    if not base_prompt:
        # Fallback prompt if config not found
        base_prompt = """
        Generate a fully personalized email for a user based on their stage in the customer journey.

        Stage: {stage}
        Stage Description: {stage_description}
        Next Stage: {next_stage}

        Product Information:
        - Name: {product_name}
        - Features: {product_features}
        - Summary: {product_summary}
        - URL: {product_url}

        Communication Guidelines:
        - Tone: {tone_of_voice}
        - Brand Personality: {brand_archetype}
        - Sender Name: {sender_name}
        - Length of the Mail: {email_length}
        - Style: {communication_style}

        UTM Parameters:
        - UTM Campaign: {utm_campaign}
        - UTM Content: {utm_content}
        - UTM Medium: {utm_medium}
        - UTM Source: {utm_source}

        Emoji Preferences:
        - Use emojis in subject: {use_emojis_subject}
        - Use emojis in body: {use_emojis_body}

        Generate a JSON response with:
        {{
            "subject": "Email subject line",
            "preheader": "Brief preview text",
            "body": "Email body content"
        }}
        """

    prompt_template = ChatPromptTemplate.from_template(base_prompt)
    
    for i in range(num_templates):
        try:
            # Prepare prompt variables matching repository format
            prompt_vars = {
                "first_name": "User",  # Generic since this is template generation
                "user_behavior": f"User is in {stage} stage",
                "user_stage": stage,
                "product_name": product_data.get('name', ''),
                "product_summary": product_data.get('summary', ''),
                "product_features": '\n'.join([f"- {feature}" for feature in product_data.get('features', [])]),
                "product_url": product_data.get('url', ''),
                "tone": communication_details.get('toneOfVoice', 'professional'),
                "brand_personality": communication_details.get('brandArchetype', ''),
                "tone_of_voice": communication_details.get('toneOfVoice', ''),
                "sender_name": communication_details.get('sender_name', ''),
                "length": communication_details.get('email_length', '100-150 words'),
                "stage": stage,
                "stage_description": stage_info.get('description', ''),
                "next_stage": stage_info.get('nextStage', ''),
                "communication_style": communication_details.get('communication_style', ''),
                "email_length": communication_details.get('email_length', ''),
                "utm_campaign": communication_details.get('utm_campaign', ''),
                "utm_content": communication_details.get('utm_content', ''),
                "utm_medium": communication_details.get('utm_medium', ''),
                "utm_source": communication_details.get('utm_source', ''),
                "use_emojis_subject": include_emojis.get('use_emojis_subject', False),
                "use_emojis_body": include_emojis.get('use_emojis_body', False)
            }
            
            # Generate template using async invoke like in repository
            response = await llm.ainvoke(prompt_template.format(**prompt_vars))
            
            # Parse response
            try:
                template_data = json.loads(response.content)
                
                # Add metadata
                template_data.update({
                    "stage": stage,
                    "product_name": product_data.get('name', ''),
                    "template_id": f"{stage}_{product_data.get('name', '')}_{i+1}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    "organization_url": org_url,
                    "generated_at": datetime.now().isoformat()
                })
                
                templates.append(template_data)
                
            except json.JSONDecodeError:
                # Fallback parsing if JSON is malformed
                content = response.content
                template_data = {
                    "subject": extract_field(content, "subject"),
                    "preheader": extract_field(content, "preheader"),
                    "body": extract_field(content, "body"),
                    "stage": stage,
                    "product_name": product_data.get('name', ''),
                    "template_id": f"{stage}_{product_data.get('name', '')}_{i+1}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    "organization_url": org_url,
                    "generated_at": datetime.now().isoformat()
                }
                templates.append(template_data)
                
        except Exception as e:
            print(f"Error generating template {i+1} for stage {stage}: {e}")
            continue
    
    return templates

def extract_field(content: str, field_name: str) -> str:
    """
    Extract a field value from content when JSON parsing fails.
    
    Args:
        content: Raw content string
        field_name: Field to extract
        
    Returns:
        str: Extracted field value
    """
    import re
    
    # Try to extract field using regex
    pattern = rf'"{field_name}":\s*"([^"]*)"'
    match = re.search(pattern, content, re.IGNORECASE | re.DOTALL)
    
    if match:
        return match.group(1)
    
    # Fallback: return empty string
    return ""

# Example usage function for testing
async def test_template_generation():
    """Test function to demonstrate usage"""
    test_input = {
        "product_stages": ["Awareness", "Consideration"],
        "stage_details": {
            "Awareness": {
                "description": "User is not aware of the product",
                "name": "Awareness",
                "nextStage": "Consideration"
            },
            "Consideration": {
                "description": "User is considering the product",
                "name": "Consideration",
                "nextStage": "Purchase"
            }
        },
        "num_templates_per_stage": "2",
        "include_emojis": {
            "use_emojis_subject": True,
            "use_emojis_body": False
        },
        "product_data": {
            "name": "GenAI Pinnacle Plus Program",
            "features": ["300+ Hours of Learning", "1:1 Mentorship", "50+ Projects"],
            "summary": "Advanced AI learning program",
            "url": "https://www.analyticsvidhya.com/pinnacleplus/"
        },
        "org_url": "https://www.analyticsvidhya.com/",
        "communication_details": {
            "brandArchetype": "Sage",
            "communication_style": "friendly",
            "email_length": "100-150 words",
            "sender_name": "Analytics Vidhya",
            "toneOfVoice": "Informative, Engaging",
            "utm_campaign": "product_launch",
            "utm_content": "initial",
            "utm_medium": "email",
            "utm_source": "email"
        }
    }

    result = await generate_templates_for_all_stages(json.dumps(test_input))
    return result

if __name__ == "__main__":
    # Test the function
    templates = asyncio.run(test_template_generation())
    print(f"Generated {len(templates)} templates")
    for template in templates:
        print(f"Stage: {template.get('stage')}")
        print(f"Subject: {template.get('subject')}")
        print("---")
