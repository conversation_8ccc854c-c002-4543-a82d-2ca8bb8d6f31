"""
API Template Generator Script
Generates templates for all stages based on product and organization data.

This script provides API functionality to return templates for all stages
without reading/writing JSON or CSV files. Data is passed as JSON strings via POST API.

Input Format (JSON string):
{
    "product_stages": [],
    "stage_details": {
        "Awareness": {
            "description": "",
            "name": "",
            "nextStage": ""
        }
    },
    "num_templates_per_stage": "",
    "include_emojis": {
        "use_emojis_subject": bool,
        "use_emojis_body": bool
    },
    "product_data": {
        "name": "",
        "features": [],
        "summary": "",
        "url": ""
    },
    "org_url": "",
    "communication_details": {
        "brandArchetype": "",
        "communication_style": "",
        "email_length": "",
        "sender_name": "",
        "toneOfVoice": "",
        "utm_campaign": "",
        "utm_content": "",
        "utm_medium": "",
        "utm_source": ""
    }
}

Output Format:
List of template dictionaries with subject, body, preheader for each stage
"""

import json
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def generate_templates_for_all_stages(input_data: str) -> List[Dict[str, Any]]:
    """
    Generate templates for all stages based on input data.
    
    Args:
        input_data (str): JSON string containing all required data
        
    Returns:
        List[Dict]: List of generated templates for all stages
    """
    try:
        # Parse input JSON
        data = json.loads(input_data) if isinstance(input_data, str) else input_data
        
        product_stages = data.get('product_stages', [])
        stage_details = data.get('stage_details', {})
        num_templates_per_stage = int(data.get('num_templates_per_stage', 1))
        include_emojis = data.get('include_emojis', {})
        product_data = data.get('product_data', {})
        org_url = data.get('org_url', '')
        communication_details = data.get('communication_details', {})
        
        # Initialize LLM
        llm = ChatOpenAI(
            model="gpt-4o-mini",
            temperature=0.7,
            max_tokens=1000
        )
        
        # Generate templates for each stage
        all_templates = []
        
        for stage in product_stages:
            stage_info = stage_details.get(stage, {})
            
            # Generate templates for this stage
            stage_templates = generate_stage_templates(
                llm=llm,
                stage=stage,
                stage_info=stage_info,
                product_data=product_data,
                communication_details=communication_details,
                include_emojis=include_emojis,
                num_templates=num_templates_per_stage,
                org_url=org_url
            )
            
            all_templates.extend(stage_templates)
        
        return all_templates
        
    except Exception as e:
        print(f"Error generating templates: {e}")
        return []

def generate_stage_templates(
    llm: ChatOpenAI,
    stage: str,
    stage_info: Dict[str, Any],
    product_data: Dict[str, Any],
    communication_details: Dict[str, Any],
    include_emojis: Dict[str, bool],
    num_templates: int,
    org_url: str
) -> List[Dict[str, Any]]:
    """
    Generate templates for a specific stage.
    
    Args:
        llm: Language model instance
        stage: Stage name
        stage_info: Stage details
        product_data: Product information
        communication_details: Communication settings
        include_emojis: Emoji preferences
        num_templates: Number of templates to generate
        org_url: Organization URL
        
    Returns:
        List[Dict]: Generated templates for the stage
    """
    templates = []
    
    # Create prompt template
    prompt_template = ChatPromptTemplate.from_template("""
    You are an expert email marketing copywriter. Generate a personalized email template for the following:

    Stage: {stage}
    Stage Description: {stage_description}
    Next Stage: {next_stage}
    
    Product Details:
    - Name: {product_name}
    - Features: {product_features}
    - Summary: {product_summary}
    - URL: {product_url}
    
    Communication Settings:
    - Brand Archetype: {brand_archetype}
    - Communication Style: {communication_style}
    - Email Length: {email_length}
    - Sender Name: {sender_name}
    - Tone of Voice: {tone_of_voice}
    - UTM Campaign: {utm_campaign}
    - UTM Content: {utm_content}
    - UTM Medium: {utm_medium}
    - UTM Source: {utm_source}
    
    Emoji Preferences:
    - Use emojis in subject: {use_emojis_subject}
    - Use emojis in body: {use_emojis_body}
    
    Generate an email template with:
    1. Subject line (compelling and relevant to the stage)
    2. Preheader text (complementary to subject, 50-90 characters)
    3. Email body (personalized content matching the communication style and tone)
    
    Guidelines:
    - Match the brand archetype and tone of voice
    - Keep the email length as specified
    - Include relevant product features for this stage
    - Create urgency or interest appropriate for the stage
    - Avoid using single or double quotes in the generated text
    - Use emojis only if specified in preferences
    - Make the content engaging and action-oriented
    
    Return the response in this exact JSON format:
    {{
        "subject": "Your subject line here",
        "preheader": "Your preheader text here",
        "body": "Your email body content here"
    }}
    """)
    
    for i in range(num_templates):
        try:
            # Prepare prompt variables
            prompt_vars = {
                "stage": stage,
                "stage_description": stage_info.get('description', ''),
                "next_stage": stage_info.get('nextStage', ''),
                "product_name": product_data.get('name', ''),
                "product_features": ', '.join(product_data.get('features', [])),
                "product_summary": product_data.get('summary', ''),
                "product_url": product_data.get('url', ''),
                "brand_archetype": communication_details.get('brandArchetype', ''),
                "communication_style": communication_details.get('communication_style', ''),
                "email_length": communication_details.get('email_length', ''),
                "sender_name": communication_details.get('sender_name', ''),
                "tone_of_voice": communication_details.get('toneOfVoice', ''),
                "utm_campaign": communication_details.get('utm_campaign', ''),
                "utm_content": communication_details.get('utm_content', ''),
                "utm_medium": communication_details.get('utm_medium', ''),
                "utm_source": communication_details.get('utm_source', ''),
                "use_emojis_subject": include_emojis.get('use_emojis_subject', False),
                "use_emojis_body": include_emojis.get('use_emojis_body', False)
            }
            
            # Generate template
            chain = prompt_template | llm
            response = chain.invoke(prompt_vars)
            
            # Parse response
            try:
                template_data = json.loads(response.content)
                
                # Add metadata
                template_data.update({
                    "stage": stage,
                    "product_name": product_data.get('name', ''),
                    "template_id": f"{stage}_{product_data.get('name', '')}_{i+1}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    "organization_url": org_url,
                    "generated_at": datetime.now().isoformat()
                })
                
                templates.append(template_data)
                
            except json.JSONDecodeError:
                # Fallback parsing if JSON is malformed
                content = response.content
                template_data = {
                    "subject": extract_field(content, "subject"),
                    "preheader": extract_field(content, "preheader"),
                    "body": extract_field(content, "body"),
                    "stage": stage,
                    "product_name": product_data.get('name', ''),
                    "template_id": f"{stage}_{product_data.get('name', '')}_{i+1}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    "organization_url": org_url,
                    "generated_at": datetime.now().isoformat()
                }
                templates.append(template_data)
                
        except Exception as e:
            print(f"Error generating template {i+1} for stage {stage}: {e}")
            continue
    
    return templates

def extract_field(content: str, field_name: str) -> str:
    """
    Extract a field value from content when JSON parsing fails.
    
    Args:
        content: Raw content string
        field_name: Field to extract
        
    Returns:
        str: Extracted field value
    """
    import re
    
    # Try to extract field using regex
    pattern = rf'"{field_name}":\s*"([^"]*)"'
    match = re.search(pattern, content, re.IGNORECASE | re.DOTALL)
    
    if match:
        return match.group(1)
    
    # Fallback: return empty string
    return ""

# Example usage function for testing
def test_template_generation():
    """Test function to demonstrate usage"""
    test_input = {
        "product_stages": ["Awareness", "Consideration"],
        "stage_details": {
            "Awareness": {
                "description": "User is not aware of the product",
                "name": "Awareness",
                "nextStage": "Consideration"
            },
            "Consideration": {
                "description": "User is considering the product",
                "name": "Consideration", 
                "nextStage": "Purchase"
            }
        },
        "num_templates_per_stage": "2",
        "include_emojis": {
            "use_emojis_subject": True,
            "use_emojis_body": False
        },
        "product_data": {
            "name": "GenAI Pinnacle Plus Program",
            "features": ["300+ Hours of Learning", "1:1 Mentorship", "50+ Projects"],
            "summary": "Advanced AI learning program",
            "url": "https://www.analyticsvidhya.com/pinnacleplus/"
        },
        "org_url": "https://www.analyticsvidhya.com/",
        "communication_details": {
            "brandArchetype": "Sage",
            "communication_style": "friendly",
            "email_length": "100-150 words",
            "sender_name": "Analytics Vidhya",
            "toneOfVoice": "Informative, Engaging",
            "utm_campaign": "product_launch",
            "utm_content": "initial",
            "utm_medium": "email",
            "utm_source": "email"
        }
    }
    
    result = generate_templates_for_all_stages(json.dumps(test_input))
    return result

if __name__ == "__main__":
    # Test the function
    templates = test_template_generation()
    print(f"Generated {len(templates)} templates")
    for template in templates:
        print(f"Stage: {template.get('stage')}")
        print(f"Subject: {template.get('subject')}")
        print("---")
